import { Page, TestFixture, TestInfo } from '@playwright/test';
import { WSClient } from './wsClient';
import { PageContext } from './pageContext';
import { config } from 'dotenv';
import { exportTestResult } from './testResult';
import { TestResult } from './types';
import { getAccessToken } from './testResult';

config();

export async function q(userPrompt: string): Promise<void> {
  const page = PageContext.getInstance().getPage();
  return WSClient.getInstance().sendCommand(userPrompt, page);
}

// Auto-initialize page context in test environment
import { test as base } from '@playwright/test';

type PageTestFixture = {
  page: Page;
};

let cachedAccessToken: string | null = null;

export const test = base.extend<PageTestFixture>({
  page: async ({ page }, use: (page: Page) => Promise<void>, testInfo: TestInfo) => {
    const startTime = Date.now();
    PageContext.getInstance().setPage(page);
    
    try {
      // Get access token once before the test starts
      if (!cachedAccessToken) {
        cachedAccessToken = await getAccessToken();
      }
      
      await use(page);
      
      // Extract tcId from test title (format: "12345-Test Title")
      const tcId = parseInt(testInfo.title.split('-')[0]);
      if (!isNaN(tcId) && process.env.AGENTQ_TESTRUN_ID && cachedAccessToken) {
        const executionTime = (Date.now() - startTime) / 1000;
        await exportTestResult(
          tcId.toString(),
          process.env.AGENTQ_TESTRUN_ID,
          {
            status: 'passed',
            actualResult: `Test "${testInfo.title}" passed successfully`,
            executionTime,
            notes: 'Test completed without errors'
          }
        );
      }
    } catch (error: any) {
      // Extract tcId from test title (format: "12345-Test Title")
      const tcId = parseInt(testInfo.title.split('-')[0]);
      if (!isNaN(tcId) && process.env.AGENTQ_TESTRUN_ID && cachedAccessToken) {
        const executionTime = (Date.now() - startTime) / 1000;
        await exportTestResult(
          tcId.toString(),
          process.env.AGENTQ_TESTRUN_ID,
          {
            status: 'failed',
            actualResult: error.message,
            executionTime,
            notes: `Test failed: ${error.message}`
          }
        );
      }
      throw error;
    }
  },
});

export { expect } from "@playwright/test";